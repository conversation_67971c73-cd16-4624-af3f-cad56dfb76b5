# 静默模式功能说明

## 功能概述

静默模式是一个自动化的消息处理控制功能，当好友发送消息并被忽略后，系统会自动进入静默状态，在指定时间内不处理任何消息。每次有好友发送消息都会刷新静默时长。

## 功能特性

### 1. 自动激活
- 当好友消息被忽略时，自动激活静默模式
- 静默模式激活后，所有消息都会被忽略，不进行任何处理

### 2. 时间刷新
- 每次有好友发送消息时，静默时间会重新计算
- 确保在持续收到好友消息的情况下，系统保持静默状态

### 3. 可配置
- 可以通过配置文件启用/禁用静默模式
- 可以自定义静默持续时间（分钟）

### 4. 手动控制
- 提供API接口手动激活、延长或停用静默模式
- 提供状态查询接口

## 配置说明

在 `config.json` 文件中添加以下配置：

```json
{
  "silence_mode": {
    "enabled": true,
    "duration_minutes": 10
  }
}
```

### 配置参数说明

- `enabled`: 是否启用静默模式功能（true/false）
- `duration_minutes`: 静默持续时间，单位为分钟（默认10分钟）

## API接口

### 1. 获取静默模式状态
```
GET /api/v1/silence-mode/status
```

响应示例：
```json
{
  "success": true,
  "message": "获取静默模式状态成功",
  "data": {
    "enabled": true,
    "active": true,
    "remaining_seconds": 580,
    "remaining_minutes": 9.7,
    "duration_minutes": 10
  }
}
```

### 2. 手动激活静默模式
```
POST /api/v1/silence-mode/activate
```

### 3. 延长静默模式时间
```
POST /api/v1/silence-mode/extend
```

### 4. 手动停用静默模式
```
POST /api/v1/silence-mode/deactivate
```

### 5. 获取配置信息
```
GET /api/v1/silence-mode/config
```

## 工作流程

1. **正常状态**: 系统正常处理所有有效的群聊消息

2. **好友消息被忽略**:
   - 系统检测到好友发送的消息
   - 根据好友忽略列表判断该好友需要被忽略
   - 自动激活静默模式（如果未激活）或延长静默时间（如果已激活）

3. **静默状态**:
   - 系统忽略所有非忽略好友的消息，不进行任何处理
   - **重要**：被忽略好友的消息仍会被检查，并刷新静默时间
   - 静默状态会持续配置的时间长度

4. **时间刷新机制**:
   - 即使在静默模式下，被忽略好友发送新消息时仍会刷新静默时间
   - 确保持续收到骚扰消息时，系统保持静默状态

5. **静默结束**:
   - 静默时间到期后，系统自动恢复正常消息处理
   - 或者通过API手动停用静默模式

## 日志记录

系统会记录以下关键事件：

- 静默模式激活：`静默模式已激活，持续时间: X 分钟`
- 静默模式延长：`好友消息被忽略，静默模式时间已刷新`
- 静默模式中的消息：`静默模式激活中，忽略所有消息`
- 静默模式停用：`静默模式已手动停用`

## 使用场景

1. **防止骚扰**: 当检测到骚扰消息时，自动进入静默状态，避免持续处理无效消息
2. **节省资源**: 在高频无效消息期间，减少系统资源消耗
3. **智能过滤**: 结合好友忽略功能，实现智能的消息过滤机制

## 注意事项

1. 静默模式激活时，**正常用户的消息**会被忽略，但被忽略好友的消息仍会被检查以刷新静默时间
2. 建议合理设置静默时间，避免影响正常业务
3. 可以通过API随时查看静默状态和剩余时间
4. 静默模式依赖Redis存储状态信息，确保Redis服务正常运行
5. **重要**：逻辑顺序确保即使在静默模式下，好友消息仍能刷新静默时长

## 测试验证

可以使用提供的测试脚本验证功能：

```bash
python test_silence_mode_demo.py
```

或运行单元测试：

```bash
python -m pytest tests/test_silence_service.py -v
python -m pytest tests/test_message_processor.py -k silence -v
```
