"""
主应用入口
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from loguru import logger
import time
from config import settings
from app.api.callback import router as callback_router
from app.api.friend_ignore import router as friend_ignore_router
from app.api.silence_mode import router as silence_mode_router
from app.models.database import create_tables
from app.workers.message_worker import message_worker
from app.workers.online_status_worker import online_status_worker
from app.services.contact_sync import contact_sync_service


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("正在启动应用...")

    # 创建数据库表
    try:
        create_tables()
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"数据库表创建失败: {str(e)}")

    # 启动消息工作进程
    try:
        message_worker.start()
        logger.info("消息工作进程启动成功")
    except Exception as e:
        logger.error(f"消息工作进程启动失败: {str(e)}")

    # 启动在线状态检测工作进程
    try:
        online_status_worker.start()
        logger.info("在线状态检测工作进程启动成功")
    except Exception as e:
        logger.error(f"在线状态检测工作进程启动失败: {str(e)}")

    # 同步联系人信息并建立好友忽略列表
    try:
        # 首先尝试获取当前在线的w_id
        from app.services.ecloud_client import ecloud_client
        online_list = ecloud_client.query_online_wechat_list()

        current_w_id = settings.ecloud_w_id

        # 检查是否有在线微信
        if online_list is None:
            logger.error("查询在线微信列表失败，跳过联系人同步")
        elif len(online_list) == 0:
            logger.warning("当前没有在线微信，跳过联系人同步（避免初始化通讯录列表接口调用失败）")
        else:
            # 如果有在线微信，使用在线微信的w_id
            online_w_id = online_list[0].get("wId")
            if online_w_id and online_w_id != current_w_id:
                logger.info(f"检测到在线w_id与配置不同，更新配置: {current_w_id} -> {online_w_id}")
                settings.update_ecloud_w_id(online_w_id)
                current_w_id = online_w_id

            if current_w_id:
                logger.info(f"开始同步联系人信息，使用w_id: {current_w_id}")
                success = contact_sync_service.sync_contacts_on_startup(current_w_id)
                if success:
                    logger.info("联系人同步完成，好友忽略列表已建立")
                else:
                    logger.warning("联系人同步失败")
            else:
                logger.warning("未配置ecloud_w_id，跳过联系人同步")

    except Exception as e:
        logger.error(f"联系人同步异常: {str(e)}")

    logger.info("应用启动完成")

    yield

    # 关闭时执行
    logger.info("正在关闭应用...")

    # 停止消息工作进程
    try:
        message_worker.stop()
        logger.info("消息工作进程已停止")
    except Exception as e:
        logger.error(f"消息工作进程停止失败: {str(e)}")

    # 停止在线状态检测工作进程
    try:
        online_status_worker.stop()
        logger.info("在线状态检测工作进程已停止")
    except Exception as e:
        logger.error(f"在线状态检测工作进程停止失败: {str(e)}")

    logger.info("应用已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="E云管家-DifyAI对接服务",
    description="将E云管家消息转发到DifyAI并返回AI回答",
    version="1.0.0",
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(callback_router, prefix="/api/v1", tags=["回调接口"])
app.include_router(friend_ignore_router, prefix="/api/v1", tags=["好友忽略管理"])
app.include_router(silence_mode_router, prefix="/api/v1", tags=["静默模式管理"])


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "E云管家-DifyAI对接服务",
        "version": "1.0.0",
        "status": "running",
    }


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "message": "E云管家-DifyAI对接服务运行正常",
        "timestamp": int(time.time()),
    }


if __name__ == "__main__":
    # 配置日志
    logger.add(
        settings.log_file,
        rotation="1 day",
        retention="7 days",
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}",
    )

    logger.info("启动E云管家-DifyAI对接服务")

    # 启动服务
    uvicorn.run(
        "main:app",
        host=settings.server_host,
        port=settings.server_port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )
