# 在线状态监控功能说明

## 功能概述

在线状态监控功能会定期检测E云管家中微信账号的在线状态，当检测到没有在线的微信账号时，会自动发送邮件和短信通知管理员。

## 功能特性

- **定时检测**: 每5分钟（可配置）检测一次微信在线状态
- **多渠道通知**: 支持邮件和短信两种通知方式
- **通知冷却**: 30分钟内不会重复发送相同的掉线通知
- **灵活配置**: 支持启用/禁用各个功能模块
- **多收件人**: 支持配置多个邮箱地址和手机号码
- **动态w_id更新**: 自动检测并更新微信重新登录后的w_id变化

## 配置说明

### 1. 在线状态监控配置

```json
"online_status_monitor": {
  "enabled": true,                    // 是否启用在线状态监控
  "check_interval_minutes": 5,        // 检测间隔（分钟）
  "notification_message": "微信掉线提醒：当前没有在线的微信账号，请及时检查！"  // 通知消息内容
}
```

### 2. 邮件通知配置

```json
"email_notification": {
  "enabled": true,                    // 是否启用邮件通知
  "smtp_server": "smtp.qq.com",       // SMTP服务器地址
  "smtp_port": 587,                   // SMTP端口
  "smtp_username": "<EMAIL>",  // SMTP用户名
  "smtp_password": "your_app_password",   // SMTP密码（QQ邮箱使用授权码）
  "from_email": "<EMAIL>",      // 发件人邮箱
  "to_emails": ["<EMAIL>", "<EMAIL>"]  // 收件人邮箱列表
}
```

#### 邮件配置说明

- **QQ邮箱**: 需要开启SMTP服务并使用授权码作为密码
- **Gmail**: 需要开启两步验证并使用应用专用密码
- **企业邮箱**: 根据邮箱服务商提供的SMTP配置进行设置

### 3. 短信通知配置

```json
"sms_notification": {
  "enabled": true,                    // 是否启用短信通知
  "api_url": "https://smsapi.izjun.com:8443/sms/api/sendMessageMass",  // 短信API地址
  "username": "your_sms_username",    // 短信平台用户名
  "password": "your_sms_password",    // 短信平台密码
  "phone_numbers": ["13800138000", "13900139000"]  // 接收短信的手机号码列表
}
```

#### 短信配置说明

- 短信接口使用MD5签名验证，确保账号密码正确
- 支持批量发送到多个手机号码
- 短信内容会自动去除时间戳以避免过长

## 使用方法

### 1. 配置文件设置

1. 复制 `config.example.json` 为 `config.json`
2. 根据实际情况修改配置参数
3. 确保邮箱和短信服务的账号密码正确

### 2. 启动服务

服务启动时会自动启动在线状态监控功能：

```bash
python main.py
```

### 3. 查看日志

监控状态和通知发送情况会记录在日志中：

```
2025-07-28 10:00:00 | INFO | 开始检测微信在线状态
2025-07-28 10:00:01 | INFO | 当前在线微信数量: 2
2025-07-28 10:05:00 | WARNING | 检测到微信全部掉线，发送通知
2025-07-28 10:05:02 | INFO | 掉线通知邮件发送成功
2025-07-28 10:05:03 | INFO | 掉线通知短信发送成功
```

## 测试功能

### 1. 运行测试

```bash
python -m pytest tests/test_online_status_monitor.py -v
```

### 2. 手动测试邮件服务

```python
from app.services.email_service import email_service

# 测试连接
success = email_service.test_connection()
print(f"邮件服务连接测试: {'成功' if success else '失败'}")

# 发送测试邮件
success = email_service.send_notification("测试邮件", "这是一封测试邮件")
print(f"测试邮件发送: {'成功' if success else '失败'}")
```

### 3. 手动测试短信服务

```python
from app.services.sms_service import sms_service

# 发送测试短信
success = sms_service.test_connection()
print(f"短信服务测试: {'成功' if success else '失败'}")
```

## 故障排除

### 1. 邮件发送失败

- 检查SMTP服务器地址和端口是否正确
- 确认用户名和密码（授权码）是否正确
- 检查网络连接和防火墙设置
- 查看详细错误日志
- 注意：如果看到"邮件发送成功"日志，说明邮件已正常发送，可以忽略连接关闭时的异常

### 2. 短信发送失败

- 检查短信平台账号余额
- 确认用户名和密码是否正确
- 检查手机号码格式是否正确
- 查看API返回的错误码和消息

### 3. 在线状态检测异常

- 检查E云管家服务是否正常运行
- 确认E云管家API地址和授权token是否正确
- 检查网络连接状态

## 动态w_id更新功能

### 功能说明

当微信重新登录后，E云管家会分配新的w_id，原有配置文件中的w_id将失效。本系统实现了动态w_id更新功能：

1. **启动时检测**: 应用启动时会查询当前在线的微信列表，如果发现w_id与配置不同，会自动更新配置文件
2. **运行时检测**: 在线状态监控过程中，每次检测到有在线微信时，都会检查w_id是否发生变化
3. **自动更新**: 发现w_id变化时，会同时更新内存配置和配置文件，确保后续API调用使用正确的w_id
4. **线程安全**: 使用线程锁确保多线程环境下的配置更新安全

### 更新日志

系统会在日志中记录w_id的变化：

```
2025-07-28 10:00:00 | INFO | 检测到w_id变化: old-w-id-123 -> new-w-id-456
2025-07-28 10:00:01 | INFO | 成功更新w_id: old-w-id-123 -> new-w-id-456
```

## 注意事项

1. **通知频率**: 系统设置了30分钟的通知冷却时间，避免频繁发送重复通知
2. **服务依赖**: 功能依赖E云管家API服务，确保API服务正常运行
3. **配置安全**: 邮箱密码和短信密码等敏感信息请妥善保管
4. **成本控制**: 短信发送会产生费用，请合理设置检测频率和接收号码数量
5. **w_id更新**: 系统会自动处理微信重新登录后的w_id变化，无需手动修改配置文件
6. **联系人同步**: 系统只会在检测到有在线微信时才进行联系人同步，避免无效调用
